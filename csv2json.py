import csv
import json
import os

def find_csv_files(directory):
    csv_files = []
    for root, _, files in os.walk(directory):  # 递归遍历目录
        for file in files:
            if file.endswith('.csv'):
                csv_files.append(os.path.join(root, file))  # 添加完整路径
    return csv_files

def convert_to_list(value, is_number=False):
    # 将字符串转换为列表
    if not value:  # 检查是否为空
        return [] if is_number else ['']  # 返回空列表或包含空字符串的列表
    if is_number:
        return [float(x) for x in value.split(',') if x]  # 转换为整数列表，跳过空值
    return [x.strip() for x in value.split(',')]  # 转换为字符串列表

def convert_value(key, value):
    # 根据前缀转换字段类型
    if key.startswith('n_'):  # 数字类型
        if key == 'n_id':  # 空值返回0
            return int(float(value)) if value else 0  # 取整，空值返回0
        else :
            return float(value) if value else 0.0  # 取浮点数，空值返回0.0
    elif key.startswith('s_'):  # 字符串类型
        return value.strip() if value else ''  # 空值返回空字符串
    elif key.startswith('vn_'):  # 数字数组类型
        return convert_to_list(value.strip() if value else '', is_number=True)
    elif key.startswith('vs_'):  # 字符串数组类型
        return convert_to_list(value.strip() if value else '')
    elif key.startswith('o_'):  # JSON字符串类型
        return json.loads(value) if value else {}  # 解析为JSON对象，空值返回空字典
    else:
        return value  # 默认返回原值

def csv_to_json():
    current_directory = os.getcwd()  # 获取当前工作目录
    print(f"当前工作目录: {current_directory}")

    csv_files = find_csv_files(current_directory)  # 查找所有CSV文件

    if not csv_files:
        print("没有找到CSV文件，请确保当前目录及其子目录下有CSV文件。")
        return  # 如果没有找到CSV文件，退出函数

    print(f"找到的CSV文件: {csv_files}")
    for csv_file in csv_files:
        isTotals = csv_file.endswith('totals.csv')
        print(f"是否为_totals.csv: {isTotals}")
        try:
            with open(csv_file, mode='r', encoding='utf-8') as file:
                ids = []  # 用于存储ID和URL
                data = []  # 用于存储其他数据

                reader = csv.DictReader(file, delimiter='\t')  # 指定制表符为分隔符
                print(f"正在读取文件: {csv_file}")
                print(f"列名: {reader.fieldnames}")  # 打印列名

                # 修剪列名
                reader.fieldnames = [name.strip() for name in reader.fieldnames]

                # 确定ID列名
                id_column = None
                if 'n_id' in reader.fieldnames:
                    id_column = 'n_id'
                elif 'tag' in reader.fieldnames:
                    id_column = 'tag'
                else:
                    print(f"警告: 文件 {csv_file} 没有找到ID列 (n_id 或 tag)，跳过此文件")
                    continue

                for row in reader:
                    # 根据前缀转换字段类型
                    for key in row:
                        row[key] = convert_value(key, row[key])

                    # 获取ID值
                    id_value = row[id_column]

                    # 确保ID值是整数
                    if isinstance(id_value, str) and id_value.isdigit():
                        id_value = int(id_value)

                    if id_value not in [entry['id'] for entry in ids]:  # 确保ID唯一
                        # 使用相对路径格式
                        json_file_path = os.path.join(os.path.dirname(csv_file), f"{id_value}.json")  # 生成JSON文件路径
                        ids.append({
                            "id": id_value,
                            "url": './' + os.path.relpath(json_file_path, start=os.path.dirname(__file__)).replace('\\', '/')  # 计算相对路径
                        })

                    # 创建一个新的字典，去掉前缀，并过滤掉key为'备注'的字段
                    new_row = {}
                    for key, value in row.items():
                        # 获取不带前缀的键名
                        if '_' in key:
                            key_without_prefix = key.split('_', 1)[-1]
                        else:
                            key_without_prefix = key

                        # 如果键名不是'备注'，则添加到新字典中
                        if key_without_prefix != '备注':
                            new_row[key_without_prefix] = value

                    data.append(new_row)

                    if isTotals:
                        continue

                    # 将每一行数据写入对应的JSON文件
                    with open(json_file_path, 'w', encoding='utf-8') as json_file:
                        json.dump(new_row, json_file, ensure_ascii=False, indent=4)

            if isTotals == False:
                # 将所有ID和URL写入_ids.json文件
                ids_file_path = os.path.join(os.path.dirname(csv_file), '_ids.json')  # 动态获取路径
                print(f"生成的ID和URL: {ids}")
                if ids:  # 确保ids不为空
                    with open(ids_file_path, 'w', encoding='utf-8') as ids_file:
                        json.dump(ids, ids_file, ensure_ascii=False, indent=4)

            if isTotals:
                # 将所有数据 写入_totals.json文件
                totals_file_path = os.path.join(os.path.dirname(csv_file), '_totals.json')  # 动态获取路径
                if data:  # 确保ids不为空
                    with open(totals_file_path, 'w', encoding='utf-8') as totals_file:
                        json.dump(data, totals_file, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {e}")

if __name__ == "__main__":
    csv_to_json()
