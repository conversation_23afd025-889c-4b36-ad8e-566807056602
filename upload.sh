#!/bin/bash

# JSON 文件上传工具 - Linux/macOS 版本 (MD5增量上传)
# 使用方法:
#   ./upload.sh          # 正常上传模式（只上传变化的文件）
#   ./upload.sh --init   # 初始化MD5记录模式
#   ./upload.sh -i       # 初始化MD5记录模式（简写）

echo "===================================================="
echo "📦 JSON 文件上传工具 (MD5增量上传)"
echo "===================================================="
echo "🔄 只上传MD5发生变化的JSON文件"
echo

# 检查 Python 环境
echo "🔍 检查 Python 环境..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    PIP_CMD="pip"
    echo "✅ 找到 Python3: $(python3 --version)"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
    echo "✅ 找到 Python: $(python --version)"
else
    echo "❌ 未找到 Python，请先安装 Python 3.6+"
    exit 1
fi

echo

# 检查或创建虚拟环境
VENV_DIR="obs_upload_env"
echo "📦 检查虚拟环境..."
if [ ! -d "$VENV_DIR" ]; then
    echo "🔧 创建虚拟环境..."
    $PYTHON_CMD -m venv $VENV_DIR
    if [ $? -ne 0 ]; then
        echo "❌ 创建虚拟环境失败"
        exit 1
    fi
    echo "✅ 虚拟环境创建成功"
else
    echo "✅ 虚拟环境已存在"
fi

# 激活虚拟环境
echo "🔌 激活虚拟环境..."
source $VENV_DIR/bin/activate
if [ $? -ne 0 ]; then
    echo "❌ 激活虚拟环境失败"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
if python -c "import obs" &> /dev/null; then
    echo "✅ OBS SDK 已安装"
else
    echo "⚠️  未找到 OBS SDK，正在安装..."
    pip install esdk-obs-python
    if [ $? -eq 0 ]; then
        echo "✅ 依赖安装成功"
    else
        echo "❌ 依赖安装失败"
        echo "💡 提示：如果遇到权限问题，请尝试使用虚拟环境"
        exit 1
    fi
fi

echo

# 检查命令行参数
INIT_MODE=false
if [ "$1" = "--init" ] || [ "$1" = "-i" ]; then
    INIT_MODE=true
    echo "🔄 MD5记录初始化模式"
else
    echo "🚀 正常上传模式（只上传变化的文件）"
fi
echo

# 如果是初始化模式，直接运行初始化
if [ "$INIT_MODE" = true ]; then
    echo "📝 初始化MD5记录..."
    python upload_json_files_proxy.py --init
else
    # 正常上传模式，检查 Vite 服务器
    echo "🔗 运行代理模式..."
    echo "📝 检查 Vite 开发服务器是否运行..."
    if curl -s -o /dev/null -w "%{http_code}" http://**************:3000 | grep -q "200"; then
        echo "✅ Vite 服务器正在运行"
        python upload_json_files_proxy.py
    else
        echo "❌ Vite 服务器未运行"
        echo "💡 请先启动开发服务器: npm run dev"
    fi
fi

echo
echo "✅ 脚本执行完成"

# 退出虚拟环境
deactivate
