@echo off
chcp 65001 >nul

echo ====================================================
echo 📦 JSON 文件上传工具测试 (MD5增量上传)
echo ====================================================
echo 🔄 只上传MD5发生变化的JSON文件
echo.

REM 检查 Python 环境
echo 🔍 检查 Python 环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 Python
    echo 💡 请先安装 Python 3.6+
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ 找到 Python: %%i
)

echo.

REM 检查命令行参数
if "%1"=="--init" (
    echo 🔄 MD5记录初始化模式
    echo 📝 模拟初始化MD5记录...
    echo python upload_json_files_proxy.py --init
) else if "%1"=="-i" (
    echo 🔄 MD5记录初始化模式
    echo 📝 模拟初始化MD5记录...
    echo python upload_json_files_proxy.py --init
) else (
    echo 🚀 正常上传模式（只上传变化的文件）
    echo 🔗 模拟运行代理模式...
    echo python upload_json_files_proxy.py
)

echo.
echo ✅ 测试完成
pause
