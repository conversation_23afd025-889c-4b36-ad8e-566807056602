#!/usr/bin/env python3
"""
华为云 OBS JSON 文件上传工具 - 代理版本
使用与 Node.js 相同的代理方式连接 OBS
"""

import os
import glob
import time
import hashlib
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import logging

try:
    from obs import ObsClient, PutObjectHeader
    from obs.model import Object
except ImportError:
    print("错误: 请先安装华为云 OBS SDK")
    print("运行: pip install esdk-obs-python")
    exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('upload_log_proxy.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MD5Manager:
    """MD5 文件管理器，用于跟踪JSON文件的MD5变化"""

    def __init__(self, md5_file_path: str = "md5.json"):
        self.md5_file_path = md5_file_path
        self.md5_records = {}
        self.load_md5_records()

    def load_md5_records(self) -> None:
        """从md5.json文件加载MD5记录"""
        try:
            if os.path.exists(self.md5_file_path):
                with open(self.md5_file_path, 'r', encoding='utf-8') as f:
                    self.md5_records = json.load(f)
                logger.info(f"📋 已加载 {len(self.md5_records)} 个文件的MD5记录")
            else:
                logger.info("📋 md5.json文件不存在，将创建新的MD5记录")
                self.md5_records = {}
        except Exception as e:
            logger.error(f"加载MD5记录失败: {str(e)}")
            self.md5_records = {}

    def save_md5_records(self) -> None:
        """保存MD5记录到md5.json文件"""
        try:
            with open(self.md5_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.md5_records, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 已保存 {len(self.md5_records)} 个文件的MD5记录到 {self.md5_file_path}")
        except Exception as e:
            logger.error(f"保存MD5记录失败: {str(e)}")

    def calculate_file_md5(self, file_path: str) -> str:
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件 {file_path} MD5 失败: {str(e)}")
            return ""

    def has_file_changed(self, file_path: str) -> bool:
        """检查文件是否发生变化（基于MD5）"""
        current_md5 = self.calculate_file_md5(file_path)
        if not current_md5:
            return False

        # 使用相对路径作为键
        relative_path = os.path.relpath(file_path)
        stored_md5 = self.md5_records.get(relative_path)

        if stored_md5 is None:
            logger.info(f"🆕 新文件: {relative_path}")
            return True

        if stored_md5 != current_md5:
            logger.info(f"🔄 文件已变化: {relative_path}")
            logger.debug(f"   旧MD5: {stored_md5}")
            logger.debug(f"   新MD5: {current_md5}")
            return True

        logger.debug(f"✅ 文件未变化: {relative_path}")
        return False

    def update_file_md5(self, file_path: str) -> None:
        """更新文件的MD5记录"""
        current_md5 = self.calculate_file_md5(file_path)
        if current_md5:
            relative_path = os.path.relpath(file_path)
            self.md5_records[relative_path] = current_md5
            logger.debug(f"📝 已更新MD5记录: {relative_path} -> {current_md5}")

    def get_changed_files(self, file_list: List[str]) -> List[str]:
        """获取发生变化的文件列表"""
        changed_files = []
        for file_path in file_list:
            if self.has_file_changed(file_path):
                changed_files.append(file_path)
        return changed_files

    def update_multiple_files(self, file_list: List[str]) -> None:
        """批量更新多个文件的MD5记录"""
        for file_path in file_list:
            self.update_file_md5(file_path)

class OBSConfigProxy:
    """OBS 配置类 - 代理版本，对应 obsClient.js 中的配置"""
    def __init__(self):
        self.access_key = 'VOG0TZ1NKSUHGPSWA6HS'
        self.secret_key = 'gzUzXAuFtq2rkBixaZAFnjCYOGii393SiYQeXMW0'
        # 关键差异：使用本地代理服务器，而不是直接连接华为云
        self.server = 'http://**************:3000/'  # 对应 Node.js 中的 window.location.host
        self.endpoint = 'obs.ap-southeast-1.myhuaweicloud.com'  # 实际的华为云端点
        self.bucket = 'satworld-resource'
        self.region = 'ap-southeast-1'
        self.service = 'obs'

class JSONFileUploaderProxy:
    """JSON 文件上传器 - 代理版本"""

    def __init__(self, config: OBSConfigProxy):
        self.config = config
        self.obs_client = None
        self.upload_results = []
        self.md5_manager = MD5Manager()

    def initialize_client(self) -> bool:
        """初始化 OBS 客户端 - 使用代理服务器"""
        try:
            logger.info(f"🔌 正在通过代理服务器连接到 OBS...")
            logger.info(f"📍 代理服务器: {self.config.server}")
            logger.info(f"🎯 目标端点: {self.config.endpoint}")

            # 使用与 Node.js obsClient.js 相同的配置
            self.obs_client = ObsClient(
                access_key_id=self.config.access_key,
                secret_access_key=self.config.secret_key,
                server=self.config.server,  # 使用本地代理服务器
                path_style=True,  # 对应 Node.js 中的 path_style: true
                timeout=120,      # 对应 Node.js 中的 timeout: 120000 (转换为秒)
                max_retry_count=3,  # 对应 Node.js 中的 max_retry_count: 3
                is_signature_negotiation=False,  # 对应 Node.js 中的 is_signature_negotiation: false
                long_conn_mode=True,  # 对应 Node.js 中的 keep_alive: true
                signature='v2'  # 使用 v2 签名，适合代理模式
            )

            # 测试连接 - 通过代理
            logger.info("🧪 测试代理连接...")
            resp = self.obs_client.listBuckets()
            if resp.status < 300:
                logger.info(f"✅ 成功通过代理连接到 OBS")
                if hasattr(resp.body, 'buckets') and resp.body.buckets:
                    logger.info(f"🪣 找到 {len(resp.body.buckets)} 个存储桶")
                return True
            else:
                logger.error(f"❌ 代理连接失败: {resp.errorCode} - {resp.errorMessage}")
                return False

        except Exception as e:
            logger.error(f"初始化 OBS 客户端失败: {str(e)}")
            logger.info("💡 提示: 请确保 Vite 开发服务器正在运行 (npm run dev)")
            logger.info("💡 代理配置位于 vite.config.js 中的 '/satworld-resource' 路径")
            return False

    def find_json_files(self, directory: str = ".") -> List[str]:
        """递归查找指定目录及其子目录下的所有 .json 文件（排除特定目录和md5.json）"""
        json_files = []

        # 需要排除的目录列表
        excluded_dirs = {
            'node_modules', '.git', '.svn', '.hg',  # 版本控制和依赖目录
            'dist', 'build', 'out', '.next',       # 构建输出目录
            '.vscode', '.idea',                     # IDE 配置目录
            '__pycache__', '.pytest_cache',         # Python 缓存目录
            'coverage', '.nyc_output',              # 测试覆盖率目录
            'logs', 'tmp', 'temp',                  # 临时和日志目录
            'obs_upload_env'                        # 虚拟环境目录
        }

        # 需要排除的文件列表
        excluded_files = {
            'md5.json'  # 排除MD5记录文件本身
        }

        logger.info(f"🔍 开始递归扫描目录: {os.path.abspath(directory)}")
        logger.info(f"📁 排除的目录: {', '.join(sorted(excluded_dirs))}")
        logger.info(f"🚫 排除的文件: {', '.join(sorted(excluded_files))}")

        # 使用 os.walk 递归遍历目录
        for root, dirs, files in os.walk(directory):
            # 过滤掉需要排除的目录，修改 dirs 列表以避免进入这些目录
            dirs[:] = [d for d in dirs if d not in excluded_dirs]

            # 查找当前目录下的 .json 文件
            for file in files:
                if file.endswith('.json') and file not in excluded_files:
                    file_path = os.path.join(root, file)
                    # 规范化路径
                    file_path = os.path.normpath(file_path)
                    json_files.append(file_path)

                    # 显示找到的文件及其相对路径
                    relative_path = os.path.relpath(file_path, directory)
                    logger.info(f"  📄 找到: {relative_path}")

        # 按路径排序，便于查看
        json_files.sort()

        logger.info(f"✅ 总共找到 {len(json_files)} 个 JSON 文件")
        if json_files:
            logger.info("📋 文件列表:")
            for i, file_path in enumerate(json_files, 1):
                relative_path = os.path.relpath(file_path, directory)
                file_size = os.path.getsize(file_path) / 1024  # KB
                logger.info(f"  {i:2d}. {relative_path} ({file_size:.1f} KB)")
        else:
            logger.warning("⚠️  未找到任何 JSON 文件")

        return json_files

    def calculate_file_md5(self, file_path: str) -> str:
        """计算文件的 MD5 哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件 {file_path} MD5 失败: {str(e)}")
            return ""

    def retry_operation(self, operation, max_retries: int = 3, delay: float = 1.0, operation_name: str = "操作"):
        """通用重试机制，对应 obsService.js 中的 retry 函数"""
        for attempt in range(max_retries):
            try:
                result = operation()
                return result
            except Exception as error:
                if attempt == max_retries - 1:
                    logger.error(f"{operation_name}失败，已达最大重试次数。最终错误: {str(error)}")
                    raise error

                logger.warning(f"{operation_name}发生错误，将在 {delay}s 后重试 ({max_retries - attempt - 1} 次剩余)。错误: {str(error)}")
                time.sleep(delay)
                delay = min(delay * 2, 30)  # 指数退避，最大延迟30秒

    def upload_single_file(self, file_path: str, object_key: str = None) -> Dict:
        """
        上传单个文件到 OBS，通过代理服务器

        Args:
            file_path: 本地文件路径
            object_key: OBS 对象键，如果为 None 则使用文件名

        Returns:
            包含上传结果的字典
        """
        if object_key is None:
            object_key = f"website_public/json_files/{os.path.basename(file_path)}"

        file_size = os.path.getsize(file_path)
        file_md5 = self.calculate_file_md5(file_path)

        logger.info(f"准备上传文件: '{file_path}' 到 '{object_key}' (大小: {file_size} 字节)")
        logger.info(f"📡 通过代理服务器: {self.config.server}")

        def upload_operation():
            # 创建上传头部信息
            headers = PutObjectHeader()
            headers.contentType = 'application/json'
            headers.contentMd5 = file_md5

            # 执行上传 - 通过代理
            with open(file_path, 'rb') as file_obj:
                resp = self.obs_client.putObject(
                    bucketName=self.config.bucket,
                    objectKey=object_key,
                    content=file_obj,
                    headers=headers
                )

            if resp.status < 300:
                logger.info(f"✅ 成功上传文件: '{object_key}' (通过代理)")
                return {
                    'key': object_key,
                    'status': 'success',
                    'local_path': file_path,
                    'size': file_size,
                    'md5': file_md5,
                    'etag': resp.body.etag if hasattr(resp.body, 'etag') else None,
                    'proxy_used': True
                }
            else:
                error_detail = f"OBS上传文件错误: {resp.errorCode} - {resp.errorMessage}"
                logger.error(f"上传文件 '{object_key}' 失败. 原因: {error_detail}")
                return {
                    'key': object_key,
                    'status': 'failure',
                    'local_path': file_path,
                    'error': error_detail,
                    'proxy_used': True
                }

        try:
            result = self.retry_operation(
                upload_operation,
                max_retries=3,
                delay=1.0,
                operation_name=f"上传文件 '{file_path}' 到 '{object_key}'"
            )
            return result
        except Exception as e:
            error_msg = f"上传文件 '{file_path}' 发生异常: {str(e)}"
            logger.error(error_msg)
            return {
                'key': object_key,
                'status': 'failure',
                'local_path': file_path,
                'error': error_msg,
                'proxy_used': True
            }

    def upload_all_json_files(self, directory: str = ".") -> List[Dict]:
        """上传所有 JSON 文件（只上传MD5发生变化的文件）"""
        json_files = self.find_json_files(directory)

        if not json_files:
            logger.warning("没有找到需要上传的 JSON 文件")
            return []

        logger.info(f"🔍 检查 {len(json_files)} 个 JSON 文件的MD5变化...")

        # 获取发生变化的文件
        changed_files = self.md5_manager.get_changed_files(json_files)

        if not changed_files:
            logger.info("🎉 所有JSON文件都没有变化，无需上传")
            return []

        logger.info(f"🔄 发现 {len(changed_files)} 个文件发生变化，开始上传...")
        logger.info(f"📡 使用代理服务器: {self.config.server}")

        # 显示变化的文件列表
        for i, file_path in enumerate(changed_files, 1):
            relative_path = os.path.relpath(file_path, directory)
            logger.info(f"  {i:2d}. {relative_path}")

        results = []
        success_count = 0
        failure_count = 0
        successfully_uploaded_files = []

        for i, file_path in enumerate(changed_files, 1):
            logger.info(f"[{i}/{len(changed_files)}] 正在处理: {file_path}")

            # 生成对象键，保持文件的相对路径结构
            relative_path = os.path.relpath(file_path, directory)
            object_key = f"website_public/json_files/{relative_path}"

            result = self.upload_single_file(file_path, object_key)
            results.append(result)

            if result['status'] == 'success':
                success_count += 1
                successfully_uploaded_files.append(file_path)
                logger.info(f"✅ 成功上传: {file_path}")
            else:
                failure_count += 1
                logger.error(f"❌ 上传失败: {file_path} - {result.get('error', '未知错误')}")

            # 在文件之间添加短暂延迟，避免请求过于密集
            if i < len(changed_files):
                time.sleep(0.1)

        # 更新成功上传文件的MD5记录
        if successfully_uploaded_files:
            logger.info(f"📝 更新 {len(successfully_uploaded_files)} 个成功上传文件的MD5记录...")
            self.md5_manager.update_multiple_files(successfully_uploaded_files)
            self.md5_manager.save_md5_records()

            # 如果有文件发生变化并成功上传，则同时上传md5.json文件到CDN
            logger.info("📤 检测到文件变化，正在上传md5.json到CDN...")
            md5_upload_result = self.upload_md5_file()
            if md5_upload_result['status'] == 'success':
                logger.info("✅ md5.json文件已成功上传到CDN")
                results.append(md5_upload_result)
                success_count += 1
            else:
                logger.error(f"❌ md5.json文件上传失败: {md5_upload_result.get('error', '未知错误')}")
                results.append(md5_upload_result)
                failure_count += 1

        logger.info(f"上传完成! 成功: {success_count}, 失败: {failure_count}")
        self.upload_results = results
        return results

    def initialize_md5_records(self, directory: str = ".") -> None:
        """初始化MD5记录，为所有JSON文件创建md5.json"""
        logger.info("🔄 初始化MD5记录...")

        json_files = self.find_json_files(directory)
        if not json_files:
            logger.warning("没有找到JSON文件")
            return

        logger.info(f"📝 为 {len(json_files)} 个JSON文件计算MD5...")

        for i, file_path in enumerate(json_files, 1):
            relative_path = os.path.relpath(file_path, directory)
            logger.info(f"  [{i}/{len(json_files)}] {relative_path}")
            self.md5_manager.update_file_md5(file_path)

        self.md5_manager.save_md5_records()
        logger.info("✅ MD5记录初始化完成")

    def generate_upload_report(self) -> str:
        """生成上传报告"""
        if not self.upload_results:
            return "没有上传结果可报告"

        report_lines = [
            "=" * 60,
            "JSON 文件上传报告 (代理模式)",
            "=" * 60,
            f"上传时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"目标存储桶: {self.config.bucket}",
            f"代理服务器: {self.config.server}",
            f"目标端点: {self.config.endpoint}",
            f"连接模式: 通过 Vite 代理服务器",
            ""
        ]

        success_files = [r for r in self.upload_results if r['status'] == 'success']
        failure_files = [r for r in self.upload_results if r['status'] == 'failure']

        report_lines.extend([
            f"总文件数: {len(self.upload_results)}",
            f"成功上传: {len(success_files)}",
            f"上传失败: {len(failure_files)}",
            ""
        ])

        if success_files:
            report_lines.extend([
                "成功上传的文件:",
                "-" * 40
            ])
            for result in success_files:
                size_mb = result.get('size', 0) / 1024 / 1024
                report_lines.append(f"✅ {result['local_path']} -> {result['key']} ({size_mb:.2f} MB)")
            report_lines.append("")

        if failure_files:
            report_lines.extend([
                "上传失败的文件:",
                "-" * 40
            ])
            for result in failure_files:
                report_lines.append(f"❌ {result['local_path']} -> {result['key']}")
                report_lines.append(f"  错误: {result.get('error', '未知错误')}")
            report_lines.append("")

        report_lines.extend([
            "代理配置信息:",
            "-" * 40,
            f"📡 代理服务器: {self.config.server}",
            f"🎯 目标端点: {self.config.endpoint}",
            f"📁 存储桶: {self.config.bucket}",
            f"🔧 配置文件: vite.config.js",
            "",
            "注意事项:",
            "-" * 40,
            "✅ 使用与 Node.js 相同的代理配置",
            "🔧 需要 Vite 开发服务器运行 (npm run dev)",
            "📡 通过 /satworld-resource 代理路径访问 OBS",
            ""
        ])

        report_lines.append("=" * 60)
        return "\n".join(report_lines)

    def save_upload_report(self, filename: str = "upload_report_proxy.txt"):
        """保存上传报告到文件"""
        report = self.generate_upload_report()
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"上传报告已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存上传报告失败: {str(e)}")

    def cleanup(self):
        """清理资源"""
        if self.obs_client:
            try:
                self.obs_client.close()
                logger.info("OBS 客户端连接已关闭")
            except Exception as e:
                logger.warning(f"关闭 OBS 客户端时发生错误: {str(e)}")

def check_vite_server():
    """检查 Vite 开发服务器是否运行"""
    import requests
    try:
        response = requests.get('http://**************:3000', timeout=5)
        return True
    except:
        return False

def main():
    """主函数"""
    import sys

    # 检查命令行参数
    init_mode = len(sys.argv) > 1 and sys.argv[1] == '--init'

    if init_mode:
        logger.info("🔄 初始化MD5记录模式...")
    else:
        logger.info("🚀 开始执行 JSON 文件上传任务 (代理模式)...")

    # 初始化配置
    config = OBSConfigProxy()
    uploader = JSONFileUploaderProxy(config)

    # 如果是初始化模式，只创建md5.json文件
    if init_mode:
        try:
            uploader.initialize_md5_records()
            logger.info("🎉 MD5记录初始化完成!")
            return 0
        except Exception as e:
            logger.error(f"MD5记录初始化失败: {str(e)}")
            return 1

    # 正常上传模式，检查 Vite 服务器
    if not check_vite_server():
        logger.error("❌ Vite 开发服务器未运行!")
        logger.info("💡 请先启动开发服务器:")
        logger.info("   cd /path/to/your/project")
        logger.info("   npm run dev")
        logger.info("   然后重新运行此脚本")
        return 1

    logger.info("✅ Vite 开发服务器正在运行")

    try:
        # 初始化 OBS 客户端
        if not uploader.initialize_client():
            logger.error("无法初始化 OBS 客户端，程序退出")
            return 1

        # 上传所有 JSON 文件
        results = uploader.upload_all_json_files()

        # 生成并显示报告
        report = uploader.generate_upload_report()
        print("\n" + report)

        # 保存报告到文件
        uploader.save_upload_report()

        # 检查是否有失败的上传
        failure_count = len([r for r in results if r['status'] == 'failure'])
        if failure_count > 0:
            logger.warning(f"有 {failure_count} 个文件上传失败，请检查日志")
            return 1
        else:
            logger.info("🎉 所有文件上传成功!")
            return 0

    except KeyboardInterrupt:
        logger.info("用户中断了上传过程")
        return 1
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}")
        return 1
    finally:
        uploader.cleanup()

if __name__ == "__main__":
    import sys

    print("🔗 JSON 文件上传工具 - 代理模式 (MD5增量上传)")
    print("📡 使用与 Node.js 相同的 Vite 代理配置")
    print("🔄 只上传MD5发生变化的JSON文件")
    print("=" * 60)

    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        print("使用方法:")
        print("  python upload_json_files_proxy.py          # 正常上传模式（只上传变化的文件）")
        print("  python upload_json_files_proxy.py --init   # 初始化MD5记录模式")
        print("  python upload_json_files_proxy.py --help   # 显示帮助信息")
        print("")
        print("功能说明:")
        print("  - 创建md5.json文件记录所有JSON文件的MD5值")
        print("  - 只上传MD5发生变化的JSON文件")
        print("  - 上传成功后自动更新MD5记录")
        print("  - md5.json文件本身不会被上传")
        exit(0)

    exit(main())
